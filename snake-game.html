<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪食蛇游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .score {
            color: #e74c3c;
        }

        .high-score {
            color: #27ae60;
        }

        #gameCanvas {
            border: 3px solid #333;
            border-radius: 10px;
            background-color: #2c3e50;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .controls {
            margin-top: 20px;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        button:active {
            transform: translateY(0);
        }

        .instructions {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
        }

        .game-over h2 {
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .mobile-controls {
            display: none;
            margin-top: 20px;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 200px;
            margin-left: auto;
            margin-right: auto;
        }

        .mobile-controls button {
            padding: 15px;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .mobile-controls {
                display: grid;
            }
            
            .instructions {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪食蛇游戏</h1>
        
        <div class="game-info">
            <div class="score">得分: <span id="score">0</span></div>
            <div class="high-score">最高分: <span id="highScore">0</span></div>
        </div>

        <canvas id="gameCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button onclick="startGame()">开始游戏</button>
            <button onclick="pauseGame()">暂停/继续</button>
            <button onclick="resetGame()">重新开始</button>
        </div>

        <div class="mobile-controls">
            <div></div>
            <button onclick="changeDirection('up')">↑</button>
            <div></div>
            <button onclick="changeDirection('left')">←</button>
            <div></div>
            <button onclick="changeDirection('right')">→</button>
            <div></div>
            <button onclick="changeDirection('down')">↓</button>
            <div></div>
        </div>

        <div class="instructions">
            <p><strong>游戏说明：</strong></p>
            <p>🎮 使用方向键控制蛇的移动</p>
            <p>🍎 吃到食物可以增长身体和得分</p>
            <p>⚠️ 不要撞到墙壁或自己的身体</p>
            <p>📱 移动设备可使用下方按钮控制</p>
        </div>
    </div>

    <div class="game-over" id="gameOver">
        <h2>游戏结束！</h2>
        <p>最终得分: <span id="finalScore">0</span></p>
        <button onclick="resetGame()">再来一局</button>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const gameOverElement = document.getElementById('gameOver');
        const finalScoreElement = document.getElementById('finalScore');

        // 游戏配置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        let snake = [
            {x: 10, y: 10}
        ];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gamePaused = false;

        highScoreElement.textContent = highScore;

        // 生成随机食物位置
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // 确保食物不会生成在蛇身上
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // 绘制游戏画面
        function drawGame() {
            // 清空画布
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制蛇
            ctx.fillStyle = '#27ae60';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制蛇头（不同颜色）
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(snake[0].x * gridSize, snake[0].y * gridSize, gridSize - 2, gridSize - 2);

            // 绘制食物
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // 移动蛇
        function moveSnake() {
            if (!gameRunning || gamePaused) return;

            const head = {x: snake[0].x + dx, y: snake[0].y + dy};

            // 检查碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            // 检查是否撞到自己
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
                
                // 更新最高分
                if (score > highScore) {
                    highScore = score;
                    highScoreElement.textContent = highScore;
                    localStorage.setItem('snakeHighScore', highScore);
                }
            } else {
                snake.pop();
            }
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            finalScoreElement.textContent = score;
            gameOverElement.style.display = 'block';
        }

        // 开始游戏
        function startGame() {
            if (!gameRunning) {
                gameRunning = true;
                gamePaused = false;
                gameOverElement.style.display = 'none';
                generateFood();
                gameLoop();
            }
        }

        // 暂停游戏
        function pauseGame() {
            if (gameRunning) {
                gamePaused = !gamePaused;
            }
        }

        // 重置游戏
        function resetGame() {
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            gameRunning = false;
            gamePaused = false;
            gameOverElement.style.display = 'none';
            generateFood();
            drawGame();
        }

        // 改变方向
        function changeDirection(direction) {
            if (!gameRunning || gamePaused) return;

            switch(direction) {
                case 'up':
                    if (dy !== 1) { dx = 0; dy = -1; }
                    break;
                case 'down':
                    if (dy !== -1) { dx = 0; dy = 1; }
                    break;
                case 'left':
                    if (dx !== 1) { dx = -1; dy = 0; }
                    break;
                case 'right':
                    if (dx !== -1) { dx = 1; dy = 0; }
                    break;
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    changeDirection('up');
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    changeDirection('down');
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    changeDirection('left');
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    changeDirection('right');
                    break;
                case ' ':
                    e.preventDefault();
                    pauseGame();
                    break;
            }
        });

        // 游戏主循环
        function gameLoop() {
            moveSnake();
            drawGame();
            
            if (gameRunning) {
                setTimeout(gameLoop, 150);
            }
        }

        // 初始化游戏
        generateFood();
        drawGame();
    </script>
</body>
</html>
